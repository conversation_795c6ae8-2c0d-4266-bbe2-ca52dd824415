//
//  ChessGameUIState.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/1/27.
//

import Foundation
import SwiftUI
import ChessKit
import Combine

/// Centralized UI state for the chess game interface
/// This replaces scattered @Published properties across multiple ObservableObject classes
@MainActor
final class ChessGameUIState: ObservableObject {
    
    // MARK: - Move and Game State
    @Published var lastMove: Move?
    @Published var currentPlayer: Piece.Color = .white
    
    // MARK: - Drag and Drop State
    @Published var selectedSquare: Square?
    @Published var possibleMoves: [Square] = []
    @Published var isReverseDragActive: Bool = false
    @Published var reverseDragTarget: Square?
    @Published var reverseDragValidSources: [Square] = []
    
    // MARK: - Promotion Dialog State
    @Published var promotionStartSquare: Square?
    @Published var promotionEndSquare: Square?
    @Published var showPromotionDialog = false
    
    // MARK: - Variation Creation Dialog State
    @Published var showVariationCreationDialog = false
    @Published var pendingMove: Move?
    @Published var pendingMoveFromIndex: MoveTree.MoveIndex?
    @Published var existingNextMoveIndex: MoveTree.MoveIndex?
    
    // MARK: - Navigation State
    @Published var showVariationSelection = false
    @Published var availableVariations: [VariationOption] = []
    @Published var isKeyboardNavigationDisabled = false
    
    // MARK: - Move Display State
    @Published var cachedMoves: [MoveDisplayItem] = []
    @Published var hasOnlyCurrentMoveChanged: Bool = false
    
    // MARK: - Engine State
    @Published var engineState: EngineState = .stopped
    @Published var currentEvaluation: EngineEvaluation?
    @Published var engineLines: [EngineLine] = []
    @Published var engineInfo: EngineInfo?
    
    // MARK: - Board and Visual State
    @Published var isBoardFlipped: Bool = false
    @Published var currentAnnotationColor: Move.VisualAnnotations.AnnotationColor = .green
    
    // MARK: - File Operations State
    @Published var isFileOperationInProgress: Bool = false
    @Published var lastFileOperationResult: String?
    @Published var lastFileOperationSuccess: Bool = true
    
    // MARK: - Performance and Debug State
    @Published var isPerformanceMonitoringEnabled: Bool = false
    @Published var lastPerformanceMetrics: [String: Double] = [:]
    
    // MARK: - Initialization
    init() {
        print("🎨 ChessGameUIState initialized")
    }
    
    deinit {
        print("🎨 ChessGameUIState deinitialized")
    }
    
    // MARK: - State Update Methods
    
    /// Updates the last move and related state
    func updateLastMove(_ move: Move?) {
        lastMove = move
    }
    
    /// Updates the current player
    func updateCurrentPlayer(_ player: Piece.Color) {
        currentPlayer = player
    }
    
    /// Updates drag and drop state
    func updateDragState(
        selectedSquare: Square? = nil,
        possibleMoves: [Square]? = nil,
        isReverseDragActive: Bool? = nil,
        reverseDragTarget: Square? = nil,
        reverseDragValidSources: [Square]? = nil
    ) {
        if let selectedSquare = selectedSquare {
            self.selectedSquare = selectedSquare
        }
        if let possibleMoves = possibleMoves {
            self.possibleMoves = possibleMoves
        }
        if let isReverseDragActive = isReverseDragActive {
            self.isReverseDragActive = isReverseDragActive
        }
        if let reverseDragTarget = reverseDragTarget {
            self.reverseDragTarget = reverseDragTarget
        }
        if let reverseDragValidSources = reverseDragValidSources {
            self.reverseDragValidSources = reverseDragValidSources
        }
    }
    
    /// Clears all drag and drop selections
    func clearDragState() {
        selectedSquare = nil
        possibleMoves = []
        isReverseDragActive = false
        reverseDragTarget = nil
        reverseDragValidSources = []
    }
    
    /// Updates promotion dialog state
    func updatePromotionState(
        startSquare: Square? = nil,
        endSquare: Square? = nil,
        showDialog: Bool? = nil
    ) {
        if let startSquare = startSquare {
            promotionStartSquare = startSquare
        }
        if let endSquare = endSquare {
            promotionEndSquare = endSquare
        }
        if let showDialog = showDialog {
            showPromotionDialog = showDialog
        }
    }
    
    /// Clears promotion dialog state
    func clearPromotionState() {
        promotionStartSquare = nil
        promotionEndSquare = nil
        showPromotionDialog = false
    }
    
    /// Updates variation creation dialog state
    func updateVariationCreationState(
        showDialog: Bool? = nil,
        pendingMove: Move? = nil,
        pendingMoveFromIndex: MoveTree.MoveIndex? = nil,
        existingNextMoveIndex: MoveTree.MoveIndex? = nil
    ) {
        if let showDialog = showDialog {
            showVariationCreationDialog = showDialog
        }
        if let pendingMove = pendingMove {
            self.pendingMove = pendingMove
        }
        if let pendingMoveFromIndex = pendingMoveFromIndex {
            self.pendingMoveFromIndex = pendingMoveFromIndex
        }
        if let existingNextMoveIndex = existingNextMoveIndex {
            self.existingNextMoveIndex = existingNextMoveIndex
        }
    }
    
    /// Clears variation creation dialog state
    func clearVariationCreationState() {
        showVariationCreationDialog = false
        pendingMove = nil
        pendingMoveFromIndex = nil
        existingNextMoveIndex = nil
    }
    
    /// Updates navigation state
    func updateNavigationState(
        showVariationSelection: Bool? = nil,
        availableVariations: [VariationOption]? = nil,
        isKeyboardNavigationDisabled: Bool? = nil
    ) {
        if let showVariationSelection = showVariationSelection {
            self.showVariationSelection = showVariationSelection
        }
        if let availableVariations = availableVariations {
            self.availableVariations = availableVariations
        }
        if let isKeyboardNavigationDisabled = isKeyboardNavigationDisabled {
            self.isKeyboardNavigationDisabled = isKeyboardNavigationDisabled
        }
    }
    
    /// Clears navigation state
    func clearNavigationState() {
        showVariationSelection = false
        availableVariations = []
        isKeyboardNavigationDisabled = false
    }
    
    /// Updates move display cache
    func updateMoveDisplayState(
        cachedMoves: [MoveDisplayItem]? = nil,
        hasOnlyCurrentMoveChanged: Bool? = nil
    ) {
        if let cachedMoves = cachedMoves {
            self.cachedMoves = cachedMoves
        }
        if let hasOnlyCurrentMoveChanged = hasOnlyCurrentMoveChanged {
            self.hasOnlyCurrentMoveChanged = hasOnlyCurrentMoveChanged
        }
    }
    
    /// Updates engine state
    func updateEngineState(
        state: EngineState? = nil,
        evaluation: EngineEvaluation? = nil,
        lines: [EngineLine]? = nil,
        info: EngineInfo? = nil
    ) {
        if let state = state {
            engineState = state
        }
        if let evaluation = evaluation {
            currentEvaluation = evaluation
        }
        if let lines = lines {
            engineLines = lines
        }
        if let info = info {
            engineInfo = info
        }
    }
    
    /// Updates board visual state
    func updateBoardState(
        isBoardFlipped: Bool? = nil,
        annotationColor: Move.VisualAnnotations.AnnotationColor? = nil
    ) {
        if let isBoardFlipped = isBoardFlipped {
            self.isBoardFlipped = isBoardFlipped
        }
        if let annotationColor = annotationColor {
            currentAnnotationColor = annotationColor
        }
    }
    
    /// Updates file operations state
    func updateFileOperationState(
        isInProgress: Bool? = nil,
        result: String? = nil,
        success: Bool? = nil
    ) {
        if let isInProgress = isInProgress {
            isFileOperationInProgress = isInProgress
        }
        if let result = result {
            lastFileOperationResult = result
        }
        if let success = success {
            lastFileOperationSuccess = success
        }
    }
    
    /// Updates performance monitoring state
    func updatePerformanceState(
        isEnabled: Bool? = nil,
        metrics: [String: Double]? = nil
    ) {
        if let isEnabled = isEnabled {
            isPerformanceMonitoringEnabled = isEnabled
        }
        if let metrics = metrics {
            lastPerformanceMetrics = metrics
        }
    }
    
    // MARK: - Convenience Methods
    
    /// Resets all dialog states
    func clearAllDialogs() {
        clearPromotionState()
        clearVariationCreationState()
        clearNavigationState()
    }
    
    /// Resets all interaction states
    func clearAllInteractionStates() {
        clearDragState()
        clearAllDialogs()
    }
    
    /// Gets a summary of current UI state for debugging
    func getStateSummary() -> String {
        var summary = ["ChessGameUIState Summary:"]
        summary.append("- Last move: \(lastMove?.metaMove?.displayDescription ?? "None")")
        summary.append("- Current player: \(currentPlayer)")
        summary.append("- Selected square: \(selectedSquare?.notation ?? "None")")
        summary.append("- Promotion dialog: \(showPromotionDialog)")
        summary.append("- Variation dialog: \(showVariationCreationDialog)")
        summary.append("- Navigation dialog: \(showVariationSelection)")
        summary.append("- Engine state: \(engineState)")
        summary.append("- Board flipped: \(isBoardFlipped)")
        return summary.joined(separator: "\n")
    }
}
