//
//  ChessGameEventBus.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/1/27.
//

import Foundation
import SwiftUI
import ChessKit
import Combine

// MARK: - Chess Game Events

/// Comprehensive enumeration of all chess game events
enum ChessGameEvent {
    // MARK: - Move Events
    case moveCompleted(Move)
    case moveAttempted(from: Square, to: Square)
    case moveExecutionFailed(from: Square, to: Square, reason: String)
    case promotionNeeded(from: Square, to: Square)
    case variationCreationNeeded(move: Move, fromIndex: MoveTree.MoveIndex, existingIndex: MoveTree.MoveIndex?)
    
    // MARK: - Navigation Events
    case navigationRequested(NavigationType)
    case navigationCompleted(to: MoveTree.MoveIndex)
    case variationSelectionRequested(mainLine: MoveTree.MoveIndex, variations: [MoveTree.MoveIndex])
    case variationSelected(MoveTree.MoveIndex)
    case variationSelectionCancelled
    
    // MARK: - Drag and Drop Events
    case dragStarted(from: Square, piece: Piece)
    case dragEnded(from: Square, to: Square)
    case dragCancelled
    case squareSelected(Square)
    case squareDeselected
    case reverseDragStarted(target: Square)
    case reverseDragCompleted(from: Square, to: Square)
    case reverseDragCancelled
    
    // MARK: - UI State Events
    case dialogRequested(DialogType)
    case dialogDismissed(DialogType)
    case boardFlipped(Bool)
    case annotationColorChanged(Move.VisualAnnotations.AnnotationColor)
    
    // MARK: - Game State Events
    case gameLoaded(Game)
    case gameReset
    case gameModified
    case positionChanged(Position)
    case currentMoveIndexChanged(MoveTree.MoveIndex)
    
    // MARK: - Engine Events
    case engineStateChanged(EngineState)
    case engineEvaluationUpdated(EngineEvaluation?)
    case engineLinesUpdated([EngineLine])
    case analysisRequested(Position)
    case analysisCompleted(Position, EngineEvaluation)
    
    // MARK: - Annotation Events
    case squareHighlightToggled(Square, Move.VisualAnnotations.AnnotationColor)
    case arrowToggled(from: Square, to: Square, Move.VisualAnnotations.AnnotationColor)
    case textCommentUpdated(String)
    case positionAssessmentChanged(MetaMove.Assessment)
    
    // MARK: - File Operations Events
    case fileImportRequested(String) // PGN content
    case fileExportRequested
    case fileOperationCompleted(success: Bool, message: String?)
    case fileOperationFailed(error: String)
    
    // MARK: - Cache Events
    case cacheInvalidationRequested
    case cacheUpdated
    
    // MARK: - Performance Events
    case performanceMetricUpdated(String, Double) // metric name, value
}

// MARK: - Supporting Enums

/// Types of navigation operations
enum NavigationType {
    case previous
    case next
    case start
    case end
    case toMove(MoveTree.MoveIndex)
}

/// Types of dialogs in the chess game
enum DialogType {
    case promotion
    case variationCreation
    case variationSelection
    case fileImport
    case fileExport
    case engineSettings
    case gameMetadata
}

// MARK: - Event Bus Implementation

/// Central event bus for chess game events using Combine framework
@MainActor
final class ChessGameEventBus: ObservableObject {
    
    // MARK: - Private Properties
    private let eventSubject = PassthroughSubject<ChessGameEvent, Never>()
    private var subscriptions: [UUID: AnyCancellable] = [:]
    private var eventHistory: [ChessGameEvent] = []
    private let maxHistorySize = 100
    
    // MARK: - Public Properties
    
    /// Publisher for all chess game events
    var eventPublisher: AnyPublisher<ChessGameEvent, Never> {
        eventSubject.eraseToAnyPublisher()
    }
    
    /// Current number of active subscriptions
    var activeSubscriptionCount: Int {
        subscriptions.count
    }
    
    /// Recent event history (for debugging and analytics)
    var recentEvents: [ChessGameEvent] {
        Array(eventHistory.suffix(10))
    }
    
    // MARK: - Initialization
    
    init() {
        print("🚌 ChessGameEventBus initialized")
    }
    
    deinit {
        // Cleanup synchronously in deinit - no need for MainActor here
        subscriptions.values.forEach { $0.cancel() }
        subscriptions.removeAll()
        eventHistory.removeAll()
        print("🚌 ChessGameEventBus deinitialized")
    }
    
    // MARK: - Event Publishing
    
    /// Publishes an event to all subscribers
    /// - Parameter event: The event to publish
    func publish(_ event: ChessGameEvent) {
        // Add to history
        eventHistory.append(event)
        if eventHistory.count > maxHistorySize {
            eventHistory.removeFirst()
        }
        
        // Publish event
        eventSubject.send(event)
        
        // Debug logging for important events
        logEventIfNeeded(event)
    }
    
    // MARK: - Event Subscription
    
    /// Subscribes to all events with a handler
    /// - Parameter handler: The event handler closure
    /// - Returns: Subscription ID for later unsubscription
    @discardableResult
    func subscribe(handler: @escaping (ChessGameEvent) -> Void) -> UUID {
        let id = UUID()
        let cancellable = eventPublisher.sink(receiveValue: handler)
        subscriptions[id] = cancellable
        return id
    }
    
    /// Subscribes to specific event types with a handler
    /// - Parameters:
    ///   - eventTypes: Array of event types to filter for
    ///   - handler: The event handler closure
    /// - Returns: Subscription ID for later unsubscription
    @discardableResult
    func subscribe<T>(to eventTypes: [T.Type], handler: @escaping (ChessGameEvent) -> Void) -> UUID where T: Equatable {
        let id = UUID()
        let cancellable = eventPublisher
            .filter { event in
                eventTypes.contains { eventType in
                    self.isEventOfType(event, type: eventType)
                }
            }
            .sink(receiveValue: handler)
        subscriptions[id] = cancellable
        return id
    }
    
    /// Subscribes to events using a publisher chain
    /// - Parameter publisherBuilder: A closure that builds a publisher chain from the event publisher
    /// - Returns: Subscription ID for later unsubscription
    @discardableResult
    func subscribe<P: Publisher>(
        using publisherBuilder: (AnyPublisher<ChessGameEvent, Never>) -> P,
        handler: @escaping (P.Output) -> Void
    ) -> UUID where P.Failure == Never {
        let id = UUID()
        let cancellable = publisherBuilder(eventPublisher).sink(receiveValue: handler)
        subscriptions[id] = cancellable
        return id
    }
    
    // MARK: - Subscription Management
    
    /// Unsubscribes from events using the subscription ID
    /// - Parameter subscriptionId: The ID returned from subscribe methods
    func unsubscribe(_ subscriptionId: UUID) {
        subscriptions[subscriptionId]?.cancel()
        subscriptions.removeValue(forKey: subscriptionId)
    }
    
    /// Removes all subscriptions
    func unsubscribeAll() {
        subscriptions.values.forEach { $0.cancel() }
        subscriptions.removeAll()
    }
    
    // MARK: - Event Bus Lifecycle
    
    /// Cleans up all subscriptions and resources
    func cleanup() {
        unsubscribeAll()
        eventHistory.removeAll()
    }
    
    /// Resets the event bus to initial state
    func reset() {
        cleanup()
        print("🚌 ChessGameEventBus reset")
    }
    
    // MARK: - Debugging and Analytics
    
    /// Gets statistics about event bus usage
    func getStatistics() -> EventBusStatistics {
        EventBusStatistics(
            activeSubscriptions: activeSubscriptionCount,
            totalEventsProcessed: eventHistory.count,
            recentEventCount: recentEvents.count
        )
    }
    
    // MARK: - Private Helper Methods
    
    private func isEventOfType<T>(_ event: ChessGameEvent, type: T.Type) -> Bool where T: Equatable {
        // This is a simplified type checking - in a real implementation,
        // you might want more sophisticated pattern matching
        return String(describing: event).contains(String(describing: type))
    }
    
    private func logEventIfNeeded(_ event: ChessGameEvent) {
        switch event {
        case .moveCompleted(let move):
            print("🚌 Event: Move completed - \(move.metaMove?.displayDescription ?? "Unknown")")
        case .navigationCompleted(let index):
            print("🚌 Event: Navigation completed to index \(index)")
        case .gameLoaded(_):
            print("🚌 Event: Game loaded")
        case .engineStateChanged(let state):
            print("🚌 Event: Engine state changed to \(state)")
        default:
            // Only log important events to avoid spam
            break
        }
    }
}

// MARK: - Supporting Data Structures

/// Statistics about event bus usage
struct EventBusStatistics {
    let activeSubscriptions: Int
    let totalEventsProcessed: Int
    let recentEventCount: Int
}
