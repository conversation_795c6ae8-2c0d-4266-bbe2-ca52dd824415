# MacChessBase 模块化重构计划

## 🎯 重构策略：渐进式模块化改进

基于原有 `refactor.md` 计划，将复杂的架构重构分解为独立的、可验证的小步骤。每个步骤都可以独立完成、测试和验证。使用 git 进行版本控制，无需考虑向后兼容问题。

## 📋 分阶段执行计划

### 阶段 1：基础设施准备
**目标**：建立新架构的基础组件

#### 步骤 1.1：创建事件总线基础结构
- **文件**：创建 `ChessGameEventBus.swift`
- **内容**：
  - 基础事件总线类
  - 完整的事件枚举定义
  - 事件发布/订阅机制
  - 事件生命周期管理
- **验证**：编译通过，基础功能测试

#### 步骤 1.2：创建 UI 状态结构
- **文件**：创建 `ChessGameUIState.swift`
- **内容**：
  - 完整的 `ChessGameUIState` 结构体
  - 包含所有 UI 相关状态属性
  - 状态更新方法
- **验证**：编译通过，结构体属性完整

### 阶段 2：模块直接转换
**目标**：直接重命名和重构现有模块为服务

#### 步骤 2.1：转换 MoveDisplayManager → MoveDisplayService
- **操作**：
  1. 重命名 `MoveDisplayManager.swift` → `MoveDisplayService.swift`
  2. 移除 `@ObservableObject` 和 `@Published` 属性
  3. 添加事件总线依赖
  4. 重构为纯服务类
  5. 更新所有引用此类的代码
- **验证**：编译通过，移动显示功能正常

#### 步骤 2.2：转换 ChessNavigator → ChessNavigationService
- **操作**：
  1. 重命名 `ChessNavigator.swift` → `ChessNavigationService.swift`
  2. 移除 `@ObservableObject` 和所有 `@Published` 属性
  3. 将状态管理移到事件总线
  4. 简化导航方法，移除回调
  5. 更新 ChessGameViewModel 中的引用
- **验证**：导航功能完全正常

#### 步骤 2.3：转换 ChessDragManager → ChessDragService
- **操作**：
  1. 重命名 `ChessDragManager.swift` → `ChessDragService.swift`
  2. 移除 `@ObservableObject` 相关代码
  3. 重构拖拽状态管理
  4. 使用事件总线通知状态变化
  5. 更新视图层的拖拽绑定
- **验证**：拖拽功能正常，手势识别准确

#### 步骤 2.4：转换 ChessMoveExecutor → ChessMoveService
- **操作**：
  1. 重命名 `ChessMoveExecutor.swift` → `ChessMoveService.swift`
  2. 移除 `@ObservableObject` 和复杂回调链
  3. 简化移动执行逻辑
  4. 使用事件总线处理移动完成通知
  5. 更新相关的移动处理代码
- **验证**：移动执行正确，变体创建正常

### 阶段 3：ViewModel 重构
**目标**：重构 ChessGameViewModel，集成新服务和事件总线

#### 步骤 3.1：ViewModel 事件总线集成
- **操作**：
  1. 在 ChessGameViewModel 中添加事件总线实例
  2. 添加 ChessGameUIState 属性
  3. 设置事件订阅和处理逻辑
  4. 移除所有 @ObservedObject 服务属性
- **验证**：编译通过，事件流基础架构就位

#### 步骤 3.2：UI 状态完全迁移
- **操作**：
  1. 将 ViewModel 中所有 UI 相关的 @Published 属性移到 ChessGameUIState
  2. 更新所有状态访问为通过 uiState
  3. 移除手动 objectWillChange.send() 调用
  4. 重构状态更新逻辑使用事件驱动
- **验证**：UI 状态管理正确，所有绑定正常工作

#### 步骤 3.3：服务方法集成
- **操作**：
  1. 更新 ViewModel 的公共方法调用新服务
  2. 移除旧的模块方法调用
  3. 简化方法实现，使用事件总线
  4. 更新所有服务初始化代码
- **验证**：所有功能正常，方法调用简化

### 阶段 4：视图层更新和清理
**目标**：更新视图层使用新接口，清理冗余代码

#### 步骤 4.1：更新视图绑定
- **操作**：
  1. 更新所有视图使用 viewModel.uiState 绑定
  2. 移除对旧服务属性的直接访问
  3. 简化视图代码逻辑
  4. 更新手势和交互处理
- **验证**：UI 功能完全正常，代码更简洁

#### 步骤 4.2：测试更新和最终清理
- **操作**：
  1. 更新所有单元测试适配新架构
  2. 移除未使用的代码和导入
  3. 优化性能和内存使用
  4. 更新代码文档和注释
- **验证**：所有测试通过，代码质量提升，性能改善

## 🔧 执行指南

### 每个步骤的标准流程
1. **准备**：确保当前代码状态良好，提交当前更改
2. **实现**：按计划执行具体操作
3. **编译**：确保代码编译通过
4. **测试**：运行相关测试验证功能
5. **手动验证**：测试关键用户路径
6. **提交**：提交更改并添加清晰的提交信息

### 验证标准
- **编译成功**：代码无编译错误和警告
- **功能完整**：所有现有功能正常工作
- **测试通过**：相关单元测试和集成测试通过
- **性能稳定**：性能不低于重构前水平

## 📊 预期收益

### 架构改进
- 消除嵌套 ObservableObject 链
- 简化数据流和状态管理
- 提高代码可维护性和可测试性
- 减少内存使用和提升性能

### 开发体验
- 更清晰的职责分离
- 更简单的调试和问题定位
- 更容易添加新功能
- 更一致的代码模式

这个重构计划分为 4 个阶段 8 个步骤，每个步骤都直接且明确，无需考虑向后兼容，可以充分利用 git 的版本控制优势。
